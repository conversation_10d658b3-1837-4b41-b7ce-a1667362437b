import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout,
  Menu,
  Typography,
  Button,
  Form,
  Input,
  Switch,
  Select,
  Upload,
  message,
} from 'antd';
import {
  SettingOutlined,
  BuildOutlined,
  BgColorsOutlined,
  CreditCardOutlined,
  UploadOutlined,
  SaveOutlined,
  CarOutlined,
  WechatOutlined,
  CalculatorOutlined,
  PercentageOutlined,
  MobileOutlined,
  HomeOutlined,
  ShoppingOutlined,
  TagsOutlined,
  AppstoreOutlined,
  UserOutlined,
  HeartOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';

// Import shared components
import { PageContainer, Card, CardContent } from '../components/shared/StyledComponents';
import PageHeader from '../components/shared/PageHeader';

const { Sider, Content } = Layout;
const { Title } = Typography;
const { Option } = Select;

const SettingsContainer = styled.div`
  font-family: 'Be Vietnam Pro', sans-serif;
  min-height: 100vh;
  background: #f8fafc;

  .settings-layout {
    background: #f8fafc;
    min-height: 100vh;
  }

  .settings-sider {
    background: #ffffff;
    border-radius: 8px;
    height: calc(100vh - 48px);
    overflow: hidden;
    border: 1px solid #e5e7eb;
  }

  .settings-menu {
    border-right: none;
    background: transparent;
    padding: 16px 0;
  }

  .settings-menu .ant-menu-item {
    margin: 0;
    border-radius: 0;
    width: 100%;
    height: 48px;
    line-height: 48px;
    display: flex;
    align-items: center;
    font-family: 'Be Vietnam Pro', sans-serif;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .settings-menu .ant-menu-item-selected {
    background: #2563eb;
    color: white;
  }

  .settings-menu .ant-menu-item:hover {
    background: #f9fafb;
    color: #2563eb;
  }

  .settings-menu .ant-menu-item-selected:hover {
    background: #1d4ed8;
    color: white;
  }

  .settings-content {
    background: #f8fafc;
  }

  .settings-form .ant-form-item-label > label {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-weight: 500;
    color: #374151;
  }

  .settings-form .ant-form-item {
    margin-bottom: 20px;
  }

  .settings-form .ant-input,
  .settings-form .ant-select-selector,
  .settings-form .ant-input-password {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    font-family: 'Be Vietnam Pro', sans-serif;
    transition: border-color 0.2s ease;
    padding: 8px 12px;
    font-size: 14px;
  }

  .settings-form .ant-input:focus,
  .settings-form .ant-select-focused .ant-select-selector,
  .settings-form .ant-input-password:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .settings-form .ant-switch {
    background-color: #d1d5db;
  }

  .settings-form .ant-switch-checked {
    background-color: #2563eb;
  }

  .settings-form .ant-upload.ant-upload-select-picture-card {
    border: 1px dashed #d1d5db;
    border-radius: 6px;
    background-color: #fafafa;
    transition: border-color 0.2s ease;
  }

  .settings-form .ant-upload.ant-upload-select-picture-card:hover {
    border-color: #2563eb;
  }

  .settings-save-btn {
    background: #2563eb;
    border: none;
    border-radius: 6px;
    height: 40px;
    font-family: 'Be Vietnam Pro', sans-serif;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .settings-save-btn:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  }
`;

// Component cho Cài đặt chung
const GeneralSettings = () => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('shipping');

  const onFinish = (values: any) => {
    console.log('General settings:', values);
    message.success('Cài đặt đã được lưu thành công!');
  };

  const tabItems = [
    {
      key: 'shipping',
      label: 'Phí vận chuyển',
      icon: <CarOutlined />,
    },
    {
      key: 'oa',
      label: 'Cài đặt OA',
      icon: <WechatOutlined />,
    },
    {
      key: 'tax',
      label: 'Cài đặt thuế',
      icon: <CalculatorOutlined />,
    },
    {
      key: 'commission',
      label: 'Chính sách hoa hồng',
      icon: <PercentageOutlined />,
    },
  ];

  const renderShippingContent = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      className="settings-form"
      initialValues={{
        enableFreeShipping: false,
        freeShippingThreshold: 500000,
        innerCityFee: 30000,
        outerCityFee: 50000,
        provinceFee: 80000,
        remoteFee: 120000,
        enableCODFee: true,
        codFeeRate: 2,
        maxCODFee: 20000,
        enableWeightBasedFee: false,
        baseWeight: 1,
        additionalWeightFee: 5000,
      }}
    >
      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Miễn phí vận chuyển
        </Title>
      </div>

      <Form.Item label="Bật miễn phí vận chuyển" name="enableFreeShipping" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="Ngưỡng miễn phí vận chuyển (VNĐ)" name="freeShippingThreshold">
        <Input type="number" placeholder="Nhập ngưỡng miễn phí vận chuyển" />
      </Form.Item>

      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
          marginTop: 32,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Phí vận chuyển theo khu vực
        </Title>
      </div>

      <Form.Item label="Phí nội thành (VNĐ)" name="innerCityFee">
        <Input type="number" placeholder="Nhập phí vận chuyển nội thành" />
      </Form.Item>

      <Form.Item label="Phí ngoại thành (VNĐ)" name="outerCityFee">
        <Input type="number" placeholder="Nhập phí vận chuyển ngoại thành" />
      </Form.Item>

      <Form.Item label="Phí tỉnh thành khác (VNĐ)" name="provinceFee">
        <Input type="number" placeholder="Nhập phí vận chuyển tỉnh thành khác" />
      </Form.Item>

      <Form.Item label="Phí vùng xa (VNĐ)" name="remoteFee">
        <Input type="number" placeholder="Nhập phí vận chuyển vùng xa" />
      </Form.Item>

      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
          marginTop: 32,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Phí thu hộ (COD)
        </Title>
      </div>

      <Form.Item label="Bật phí thu hộ COD" name="enableCODFee" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="Tỷ lệ phí COD (%)" name="codFeeRate">
        <Input type="number" placeholder="Nhập tỷ lệ phí COD" />
      </Form.Item>

      <Form.Item label="Phí COD tối đa (VNĐ)" name="maxCODFee">
        <Input type="number" placeholder="Nhập phí COD tối đa" />
      </Form.Item>

      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
          marginTop: 32,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Phí theo trọng lượng
        </Title>
      </div>

      <Form.Item
        label="Bật tính phí theo trọng lượng"
        name="enableWeightBasedFee"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item label="Trọng lượng cơ bản (kg)" name="baseWeight">
        <Input type="number" placeholder="Nhập trọng lượng cơ bản" />
      </Form.Item>

      <Form.Item label="Phí mỗi kg thêm (VNĐ)" name="additionalWeightFee">
        <Input type="number" placeholder="Nhập phí mỗi kg thêm" />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          className="settings-save-btn"
          icon={<SaveOutlined />}
        >
          Lưu cài đặt
        </Button>
      </Form.Item>
    </Form>
  );

  const renderOAContent = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      className="settings-form"
      initialValues={{
        enableZaloOA: false,
        oaId: '',
        appId: '',
        appSecret: '',
        accessToken: '',
        refreshToken: '',
        enableOrderNotification: true,
        enablePaymentNotification: true,
        enableShippingNotification: true,
        enablePromotionNotification: false,
        templateOrderConfirm: '',
        templatePaymentConfirm: '',
        templateShippingUpdate: '',
        templateOrderComplete: '',
      }}
    >
      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Thông tin OA
        </Title>
      </div>

      <Form.Item label="Bật tích hợp Zalo OA" name="enableZaloOA" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="OA ID" name="oaId">
        <Input placeholder="Nhập OA ID" />
      </Form.Item>

      <Form.Item label="App ID" name="appId">
        <Input placeholder="Nhập App ID" />
      </Form.Item>

      <Form.Item label="App Secret" name="appSecret">
        <Input.Password placeholder="Nhập App Secret" />
      </Form.Item>

      <Form.Item label="Access Token" name="accessToken">
        <Input.Password placeholder="Nhập Access Token" />
      </Form.Item>

      <Form.Item label="Refresh Token" name="refreshToken">
        <Input.Password placeholder="Nhập Refresh Token" />
      </Form.Item>

      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
          marginTop: 32,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Cài đặt thông báo
        </Title>
      </div>

      <Form.Item
        label="Thông báo xác nhận đơn hàng"
        name="enableOrderNotification"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="Thông báo xác nhận thanh toán"
        name="enablePaymentNotification"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="Thông báo cập nhật vận chuyển"
        name="enableShippingNotification"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="Thông báo khuyến mãi"
        name="enablePromotionNotification"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
          marginTop: 32,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Template thông báo
        </Title>
      </div>

      <Form.Item label="Template xác nhận đơn hàng" name="templateOrderConfirm">
        <Input.TextArea rows={3} placeholder="Nhập template xác nhận đơn hàng" />
      </Form.Item>

      <Form.Item label="Template xác nhận thanh toán" name="templatePaymentConfirm">
        <Input.TextArea rows={3} placeholder="Nhập template xác nhận thanh toán" />
      </Form.Item>

      <Form.Item label="Template cập nhật vận chuyển" name="templateShippingUpdate">
        <Input.TextArea rows={3} placeholder="Nhập template cập nhật vận chuyển" />
      </Form.Item>

      <Form.Item label="Template hoàn thành đơn hàng" name="templateOrderComplete">
        <Input.TextArea rows={3} placeholder="Nhập template hoàn thành đơn hàng" />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          className="settings-save-btn"
          icon={<SaveOutlined />}
        >
          Lưu cài đặt
        </Button>
      </Form.Item>
    </Form>
  );

  const renderTaxContent = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      className="settings-form"
      initialValues={{
        enableVAT: true,
        vatRate: 10,
        enableImportTax: false,
        importTaxRate: 5,
        enableEnvironmentTax: false,
        environmentTaxRate: 2,
        enableSpecialTax: false,
        specialTaxRate: 15,
        taxIncluded: true,
        enableTaxExemption: false,
        taxExemptionThreshold: 1000000,
        taxRoundingMethod: 'round',
        enableTaxInvoice: true,
        invoicePrefix: 'HD',
        invoiceStartNumber: 1,
      }}
    >
      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Thuế giá trị gia tăng (VAT)
        </Title>
      </div>

      <Form.Item label="Bật thuế VAT" name="enableVAT" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="Tỷ lệ thuế VAT (%)" name="vatRate">
        <Input type="number" placeholder="Nhập tỷ lệ thuế VAT" />
      </Form.Item>

      <Form.Item label="Giá đã bao gồm thuế" name="taxIncluded" valuePropName="checked">
        <Switch />
      </Form.Item>

      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
          marginTop: 32,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Các loại thuế khác
        </Title>
      </div>

      <Form.Item label="Bật thuế nhập khẩu" name="enableImportTax" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="Tỷ lệ thuế nhập khẩu (%)" name="importTaxRate">
        <Input type="number" placeholder="Nhập tỷ lệ thuế nhập khẩu" />
      </Form.Item>

      <Form.Item
        label="Bật thuế bảo vệ môi trường"
        name="enableEnvironmentTax"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item label="Tỷ lệ thuế bảo vệ môi trường (%)" name="environmentTaxRate">
        <Input type="number" placeholder="Nhập tỷ lệ thuế bảo vệ môi trường" />
      </Form.Item>

      <Form.Item label="Bật thuế tiêu thụ đặc biệt" name="enableSpecialTax" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="Tỷ lệ thuế tiêu thụ đặc biệt (%)" name="specialTaxRate">
        <Input type="number" placeholder="Nhập tỷ lệ thuế tiêu thụ đặc biệt" />
      </Form.Item>

      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
          marginTop: 32,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Miễn thuế và làm tròn
        </Title>
      </div>

      <Form.Item label="Bật miễn thuế" name="enableTaxExemption" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="Ngưỡng miễn thuế (VNĐ)" name="taxExemptionThreshold">
        <Input type="number" placeholder="Nhập ngưỡng miễn thuế" />
      </Form.Item>

      <Form.Item label="Phương thức làm tròn" name="taxRoundingMethod">
        <Select>
          <Option value="round">Làm tròn</Option>
          <Option value="floor">Làm tròn xuống</Option>
          <Option value="ceil">Làm tròn lên</Option>
        </Select>
      </Form.Item>

      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
          marginTop: 32,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Hóa đơn thuế
        </Title>
      </div>

      <Form.Item label="Bật xuất hóa đơn thuế" name="enableTaxInvoice" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="Tiền tố hóa đơn" name="invoicePrefix">
        <Input placeholder="Nhập tiền tố hóa đơn" />
      </Form.Item>

      <Form.Item label="Số bắt đầu hóa đơn" name="invoiceStartNumber">
        <Input type="number" placeholder="Nhập số bắt đầu hóa đơn" />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          className="settings-save-btn"
          icon={<SaveOutlined />}
        >
          Lưu cài đặt
        </Button>
      </Form.Item>
    </Form>
  );

  const renderCommissionContent = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      className="settings-form"
      initialValues={{
        enableCommission: true,
        enableMultiLevel: true,
        maxLevels: 3,
        f1CommissionRate: 5,
        f2CommissionRate: 3,
        f3CommissionRate: 1,
        enableCategoryCommission: false,
        enableProductCommission: false,
        minOrderAmount: 100000,
        maxCommissionPerOrder: 1000000,
        commissionPaymentMethod: 'wallet',
        enableCommissionLimit: false,
        monthlyCommissionLimit: 10000000,
        enableCommissionHistory: true,
        commissionCalculationMethod: 'order_value',
        excludeShippingFee: true,
        excludeTax: false,
        enableCommissionApproval: true,
        autoApprovalThreshold: 500000,
      }}
    >
      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Cài đặt chung hoa hồng
        </Title>
      </div>

      <Form.Item label="Bật hệ thống hoa hồng" name="enableCommission" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="Bật hoa hồng đa cấp" name="enableMultiLevel" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="Số cấp tối đa" name="maxLevels">
        <Select>
          <Option value={2}>2 cấp (F0 → F1)</Option>
          <Option value={3}>3 cấp (F0 → F1 → F2)</Option>
          <Option value={4}>4 cấp (F0 → F1 → F2 → F3)</Option>
          <Option value={5}>5 cấp (F0 → F1 → F2 → F3 → F4)</Option>
        </Select>
      </Form.Item>

      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
          marginTop: 32,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Tỷ lệ hoa hồng theo cấp
        </Title>
        <p style={{ color: '#6b7280', fontSize: '14px', margin: 0 }}>
          F0 mua hàng → F1 và F2 nhận hoa hồng theo % đơn hàng
        </p>
      </div>

      <Form.Item
        label="Hoa hồng F1 (%)"
        name="f1CommissionRate"
        rules={[
          { required: true, message: 'Vui lòng nhập tỷ lệ hoa hồng F1!' },
          { type: 'number', min: 0, max: 100, message: 'Tỷ lệ phải từ 0-100%!' },
        ]}
      >
        <Input
          type="number"
          placeholder="Nhập tỷ lệ hoa hồng F1"
          suffix="%"
          min={0}
          max={100}
          step={0.1}
        />
      </Form.Item>

      <Form.Item
        label="Hoa hồng F2 (%)"
        name="f2CommissionRate"
        rules={[
          { required: true, message: 'Vui lòng nhập tỷ lệ hoa hồng F2!' },
          { type: 'number', min: 0, max: 100, message: 'Tỷ lệ phải từ 0-100%!' },
        ]}
      >
        <Input
          type="number"
          placeholder="Nhập tỷ lệ hoa hồng F2"
          suffix="%"
          min={0}
          max={100}
          step={0.1}
        />
      </Form.Item>

      <Form.Item
        label="Hoa hồng F3 (%)"
        name="f3CommissionRate"
        rules={[{ type: 'number', min: 0, max: 100, message: 'Tỷ lệ phải từ 0-100%!' }]}
      >
        <Input
          type="number"
          placeholder="Nhập tỷ lệ hoa hồng F3 (tùy chọn)"
          suffix="%"
          min={0}
          max={100}
          step={0.1}
        />
      </Form.Item>

      <div
        style={{
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: 16,
          marginBottom: 24,
          marginTop: 32,
        }}
      >
        <Title
          level={5}
          style={{
            marginBottom: 8,
            fontFamily: 'Be Vietnam Pro',
            color: '#374151',
            fontSize: 16,
            fontWeight: 600,
          }}
        >
          Điều kiện áp dụng
        </Title>
      </div>

      <Form.Item label="Giá trị đơn hàng tối thiểu (VNĐ)" name="minOrderAmount">
        <Input type="number" placeholder="Nhập giá trị đơn hàng tối thiểu" />
      </Form.Item>

      <Form.Item label="Hoa hồng tối đa mỗi đơn (VNĐ)" name="maxCommissionPerOrder">
        <Input type="number" placeholder="Nhập hoa hồng tối đa mỗi đơn hàng" />
      </Form.Item>

      <Form.Item label="Phương thức tính hoa hồng" name="commissionCalculationMethod">
        <Select>
          <Option value="order_value">Theo giá trị đơn hàng</Option>
          <Option value="product_value">Theo giá trị sản phẩm</Option>
          <Option value="profit_margin">Theo lợi nhuận</Option>
        </Select>
      </Form.Item>

      <Form.Item label="Loại trừ phí vận chuyển" name="excludeShippingFee" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label="Loại trừ thuế" name="excludeTax" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          className="settings-save-btn"
          icon={<SaveOutlined />}
        >
          Lưu cài đặt
        </Button>
      </Form.Item>
    </Form>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'shipping':
        return renderShippingContent();
      case 'oa':
        return renderOAContent();
      case 'tax':
        return renderTaxContent();
      case 'commission':
        return renderCommissionContent();
      default:
        return renderShippingContent();
    }
  };

  return (
    <Card>
      <PageHeader title="Cài đặt chung" description="Cấu hình các thông tin cơ bản của hệ thống" />
      <CardContent>
        <div style={{ marginBottom: 24 }}>
          <div
            style={{
              display: 'flex',
              gap: '16px',
              borderBottom: '1px solid #e5e7eb',
              marginBottom: 24,
            }}
          >
            {tabItems.map((tab) => (
              <div
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                style={{
                  padding: '12px 16px',
                  cursor: 'pointer',
                  borderBottom:
                    activeTab === tab.key ? '2px solid #2563eb' : '2px solid transparent',
                  color: activeTab === tab.key ? '#2563eb' : '#6b7280',
                  fontFamily: 'Be Vietnam Pro',
                  fontWeight: 500,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'all 0.2s ease',
                }}
              >
                {tab.icon}
                {tab.label}
              </div>
            ))}
          </div>
        </div>
        {renderContent()}
      </CardContent>
    </Card>
  );
};

// Component cho Cài đặt giao diện
const InterfaceSettings = () => {
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    console.log('Interface settings:', values);
    message.success('Cài đặt giao diện đã được lưu thành công!');
  };

  return (
    <Card>
      <PageHeader
        title="Cài đặt giao diện"
        description="Tùy chỉnh giao diện và trải nghiệm người dùng"
      />
      <CardContent>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="settings-form"
          initialValues={{
            theme: 'light',
            primaryColor: '#667eea',
            sidebarCollapsed: false,
            showBreadcrumb: true,
            showFooter: true,
            pageSize: 10,
          }}
        >
          <Form.Item label="Chủ đề giao diện" name="theme">
            <Select>
              <Option value="light">Sáng</Option>
              <Option value="dark">Tối</Option>
              <Option value="auto">Tự động</Option>
            </Select>
          </Form.Item>

          <Form.Item label="Màu chủ đạo" name="primaryColor">
            <Input type="color" style={{ width: 100, height: 40 }} />
          </Form.Item>

          <Form.Item
            label="Thu gọn sidebar mặc định"
            name="sidebarCollapsed"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item label="Hiển thị breadcrumb" name="showBreadcrumb" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item label="Hiển thị footer" name="showFooter" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item label="Số item mỗi trang" name="pageSize">
            <Select>
              <Option value={10}>10</Option>
              <Option value={20}>20</Option>
              <Option value={50}>50</Option>
              <Option value={100}>100</Option>
            </Select>
          </Form.Item>

          <Form.Item label="Logo header" name="headerLogo">
            <Upload listType="picture-card" maxCount={1} beforeUpload={() => false}>
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>Tải lên</div>
              </div>
            </Upload>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="settings-save-btn"
              icon={<SaveOutlined />}
            >
              Lưu cài đặt
            </Button>
          </Form.Item>
        </Form>
      </CardContent>
    </Card>
  );
};

// Component cho Phương thức thanh toán
const PaymentSettings = () => {
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    console.log('Payment settings:', values);
    message.success('Cài đặt phương thức thanh toán đã được lưu thành công!');
  };

  return (
    <Card>
      <PageHeader
        title="Phương thức thanh toán"
        description="Cấu hình các phương thức thanh toán cho hệ thống"
      />
      <CardContent>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="settings-form"
          initialValues={{
            enableCOD: true,
            enableBankTransfer: true,
            enableMomo: false,
            enableZaloPay: false,
            enableVNPay: false,
            bankName: 'Vietcombank',
            bankAccount: '**********',
            bankOwner: 'CÔNG TY TNHH ABC',
            momoPhone: '',
            vnpayMerchantId: '',
            vnpaySecretKey: '',
          }}
        >
          <div
            style={{
              borderBottom: '1px solid #e5e7eb',
              paddingBottom: 16,
              marginBottom: 24,
            }}
          >
            <Title
              level={5}
              style={{
                marginBottom: 8,
                fontFamily: 'Be Vietnam Pro',
                color: '#374151',
                fontSize: 16,
                fontWeight: 600,
              }}
            >
              Phương thức thanh toán khi nhận hàng
            </Title>
          </div>

          <Form.Item
            label="Thanh toán khi nhận hàng (COD)"
            name="enableCOD"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <div
            style={{
              borderBottom: '1px solid #e5e7eb',
              paddingBottom: 16,
              marginBottom: 24,
              marginTop: 32,
            }}
          >
            <Title
              level={5}
              style={{
                marginBottom: 8,
                fontFamily: 'Be Vietnam Pro',
                color: '#374151',
                fontSize: 16,
                fontWeight: 600,
              }}
            >
              Chuyển khoản ngân hàng
            </Title>
          </div>

          <Form.Item
            label="Bật chuyển khoản ngân hàng"
            name="enableBankTransfer"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item label="Tên ngân hàng" name="bankName">
            <Input placeholder="Nhập tên ngân hàng" />
          </Form.Item>

          <Form.Item label="Số tài khoản" name="bankAccount">
            <Input placeholder="Nhập số tài khoản" />
          </Form.Item>

          <Form.Item label="Chủ tài khoản" name="bankOwner">
            <Input placeholder="Nhập tên chủ tài khoản" />
          </Form.Item>

          <div
            style={{
              borderBottom: '1px solid #e5e7eb',
              paddingBottom: 16,
              marginBottom: 24,
              marginTop: 32,
            }}
          >
            <Title
              level={5}
              style={{
                marginBottom: 8,
                fontFamily: 'Be Vietnam Pro',
                color: '#374151',
                fontSize: 16,
                fontWeight: 600,
              }}
            >
              Ví điện tử
            </Title>
          </div>

          <Form.Item label="Bật MoMo" name="enableMomo" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item label="Số điện thoại MoMo" name="momoPhone">
            <Input placeholder="Nhập số điện thoại MoMo" />
          </Form.Item>

          <Form.Item label="Bật ZaloPay" name="enableZaloPay" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item label="Bật VNPay" name="enableVNPay" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item label="VNPay Merchant ID" name="vnpayMerchantId">
            <Input placeholder="Nhập VNPay Merchant ID" />
          </Form.Item>

          <Form.Item label="VNPay Secret Key" name="vnpaySecretKey">
            <Input.Password placeholder="Nhập VNPay Secret Key" />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="settings-save-btn"
              icon={<SaveOutlined />}
            >
              Lưu cài đặt
            </Button>
          </Form.Item>
        </Form>
      </CardContent>
    </Card>
  );
};

// Component cho Cài đặt công ty
const CompanySettings = () => {
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    console.log('Company settings:', values);
    message.success('Cài đặt công ty đã được lưu thành công!');
  };

  return (
    <Card>
      <PageHeader title="Cài đặt công ty" description="Thông tin chi tiết về công ty và tổ chức" />
      <CardContent>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="settings-form"
          initialValues={{
            companyName: 'Công ty TNHH ABC',
            companyAddress: '123 Đường ABC, Quận 1, TP.HCM',
            companyPhone: '0123456789',
            companyEmail: '<EMAIL>',
            taxCode: '0123456789',
            website: 'https://company.com',
          }}
        >
          <Form.Item
            label="Tên công ty"
            name="companyName"
            rules={[{ required: true, message: 'Vui lòng nhập tên công ty!' }]}
          >
            <Input placeholder="Nhập tên công ty" />
          </Form.Item>

          <Form.Item
            label="Địa chỉ công ty"
            name="companyAddress"
            rules={[{ required: true, message: 'Vui lòng nhập địa chỉ công ty!' }]}
          >
            <Input.TextArea rows={2} placeholder="Nhập địa chỉ công ty" />
          </Form.Item>

          <Form.Item
            label="Số điện thoại"
            name="companyPhone"
            rules={[{ required: true, message: 'Vui lòng nhập số điện thoại!' }]}
          >
            <Input placeholder="Nhập số điện thoại" />
          </Form.Item>

          <Form.Item
            label="Email"
            name="companyEmail"
            rules={[
              { required: true, message: 'Vui lòng nhập email!' },
              { type: 'email', message: 'Email không hợp lệ!' },
            ]}
          >
            <Input placeholder="Nhập email công ty" />
          </Form.Item>

          <Form.Item label="Mã số thuế" name="taxCode">
            <Input placeholder="Nhập mã số thuế" />
          </Form.Item>

          <Form.Item label="Website" name="website">
            <Input placeholder="Nhập website công ty" />
          </Form.Item>

          <Form.Item label="Logo công ty" name="logo">
            <Upload listType="picture-card" maxCount={1} beforeUpload={() => false}>
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>Tải lên</div>
              </div>
            </Upload>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="settings-save-btn"
              icon={<SaveOutlined />}
            >
              Lưu cài đặt
            </Button>
          </Form.Item>
        </Form>
      </CardContent>
    </Card>
  );
};

// Component cho Cài đặt giao diện Mini App
const MiniAppSettings = () => {
  const [form] = Form.useForm();
  const [appSettings, setAppSettings] = useState({
    logo: null as string | null,
    title: 'Evotech xin chào!',
    subtitle: 'Ngày mới tốt lành',
    primaryColor: '#2563eb',
  });

  const onFinish = (values: any) => {
    console.log('Mini app settings:', values);
    message.success('Cài đặt giao diện mini app đã được lưu thành công!');
  };

  const handleLogoUpload = (file: any) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      setAppSettings((prev) => ({ ...prev, logo: e.target?.result as string }));
    };
    reader.readAsDataURL(file);
    return false; // Prevent default upload
  };

  const handleSettingChange = (field: string, value: any) => {
    setAppSettings((prev) => ({ ...prev, [field]: value }));
  };

  // iPhone mockup component
  const PhoneMockup = () => (
    <div
      style={{
        width: 320,
        height: 640,
        background: '#000',
        borderRadius: 30,
        padding: 8,
        position: 'relative',
        boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
      }}
    >
      {/* iPhone notch */}
      <div
        style={{
          position: 'absolute',
          top: 8,
          left: '50%',
          transform: 'translateX(-50%)',
          width: 140,
          height: 25,
          background: '#000',
          borderRadius: 15,
          zIndex: 10,
        }}
      />

      {/* Screen */}
      <div
        style={{
          width: '100%',
          height: '100%',
          background: '#fff',
          borderRadius: 22,
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        {/* Status bar */}

        {/* Header */}
        <div
          style={{
            background: appSettings.primaryColor,
            padding: '30px 8px 10px',
            display: 'flex',
            alignItems: 'center',
            gap: 8,
          }}
        >
          {appSettings.logo ? (
            <img
              src={appSettings.logo}
              alt="Logo"
              style={{
                width: 40,
                height: 40,
                borderRadius: 8,
                objectFit: 'cover',
              }}
            />
          ) : (
            <div
              style={{
                width: 40,
                height: 40,
                background: 'rgba(255,255,255,0.2)',
                borderRadius: 8,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
              }}
            >
              <AppstoreOutlined />
            </div>
          )}
          <div>
            <div
              style={{
                color: 'white',
                fontSize: 14,
                fontWeight: 600,
                fontFamily: 'Be Vietnam Pro',
                lineHeight: 1.2,
              }}
            >
              {appSettings.title}
            </div>
            <div
              style={{
                color: 'rgba(255,255,255,0.8)',
                fontSize: 12,
                fontFamily: 'Be Vietnam Pro',
              }}
            >
              {appSettings.subtitle}
            </div>
          </div>
        </div>

        {/* Banner */}
        <div
          style={{
            height: 120,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            margin: '8px 8px',
            borderRadius: 5,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: 14,
            fontFamily: 'Be Vietnam Pro',
          }}
        >
          Banner quảng cáo
        </div>

        {/* Categories */}
        <div style={{ padding: '0 8px', marginBottom: 16 }}>
          <div
            style={{
              fontSize: 16,
              fontWeight: 600,
              marginBottom: 12,
              fontFamily: 'Be Vietnam Pro',
              color: '#1a202c',
            }}
          >
            Danh mục
          </div>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: 12,
            }}
          >
            {[
              { icon: <ShoppingOutlined />, name: 'Thời trang' },
              { icon: <MobileOutlined />, name: 'Điện tử' },
              { icon: <HomeOutlined />, name: 'Gia dụng' },
              { icon: <TagsOutlined />, name: 'Khuyến mãi' },
            ].map((category, index) => (
              <div
                key={index}
                style={{
                  textAlign: 'center',
                  padding: 8,
                }}
              >
                <div
                  style={{
                    width: 48,
                    height: 48,
                    background: `${appSettings.primaryColor}15`,
                    borderRadius: 12,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 8px',
                    color: appSettings.primaryColor,
                    fontSize: 20,
                  }}
                >
                  {category.icon}
                </div>
                <div
                  style={{
                    fontSize: 11,
                    color: '#4a5568',
                    fontFamily: 'Be Vietnam Pro',
                  }}
                >
                  {category.name}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Products */}
        <div style={{ padding: '0 8px', flex: 1 }}>
          <div
            style={{
              fontSize: 16,
              fontWeight: 600,
              marginBottom: 12,
              fontFamily: 'Be Vietnam Pro',
              color: '#1a202c',
            }}
          >
            Sản phẩm nổi bật
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 12 }}>
            {[1, 2].map((item) => (
              <div
                key={item}
                style={{
                  background: '#f7fafc',
                  borderRadius: 8,
                  padding: 8,
                }}
              >
                <div
                  style={{
                    height: 80,
                    background: '#e2e8f0',
                    borderRadius: 6,
                    marginBottom: 8,
                  }}
                />
                <div
                  style={{
                    fontSize: 12,
                    fontWeight: 500,
                    marginBottom: 4,
                    fontFamily: 'Be Vietnam Pro',
                    color: '#2d3748',
                  }}
                >
                  Sản phẩm {item}
                </div>
                <div
                  style={{
                    fontSize: 12,
                    color: appSettings.primaryColor,
                    fontWeight: 600,
                    fontFamily: 'Be Vietnam Pro',
                  }}
                >
                  299.000đ
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Navigation */}
        <div
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: 60,
            background: '#fff',
            borderTop: '1px solid #e2e8f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-around',
            paddingBottom: 8,
          }}
        >
          {[
            { icon: <HomeOutlined />, name: 'Trang chủ', active: true },
            { icon: <AppstoreOutlined />, name: 'Danh mục' },
            { icon: <ShoppingCartOutlined />, name: 'Giỏ hàng' },
            { icon: <HeartOutlined />, name: 'Yêu thích' },
            { icon: <UserOutlined />, name: 'Tài khoản' },
          ].map((nav, index) => (
            <div
              key={index}
              style={{
                textAlign: 'center',
                color: nav.active ? appSettings.primaryColor : '#a0aec0',
              }}
            >
              <div style={{ fontSize: 18, marginBottom: 2 }}>{nav.icon}</div>
              <div style={{ fontSize: 10, fontFamily: 'Be Vietnam Pro' }}>{nav.name}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <Card>
      <PageHeader
        title="Cài đặt giao diện Mini App"
        description="Tùy chỉnh giao diện và trải nghiệm người dùng cho ứng dụng di động"
      />
      <CardContent>
        <div style={{ display: 'flex', gap: 32, alignItems: 'flex-start' }}>
          {/* Phone Preview */}
          <div style={{ flex: '0 0 auto' }}>
            <div
              style={{
                marginBottom: 16,
                fontSize: 16,
                fontWeight: 600,
                fontFamily: 'Be Vietnam Pro',
                color: '#374151',
              }}
            >
              Xem trước
            </div>
            <PhoneMockup />
          </div>

          {/* Settings Panel */}
          <div style={{ flex: 1 }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              className="settings-form"
              initialValues={appSettings}
            >
              <div
                style={{
                  borderBottom: '1px solid #e5e7eb',
                  paddingBottom: 16,
                  marginBottom: 24,
                }}
              >
                <Title
                  level={5}
                  style={{
                    marginBottom: 8,
                    fontFamily: 'Be Vietnam Pro',
                    color: '#374151',
                    fontSize: 16,
                    fontWeight: 600,
                  }}
                >
                  Thông tin ứng dụng
                </Title>
              </div>

              <Form.Item label="Logo ứng dụng" name="logo">
                <Upload
                  listType="picture-card"
                  maxCount={1}
                  beforeUpload={handleLogoUpload}
                  showUploadList={false}
                >
                  <div>
                    <UploadOutlined />
                    <div style={{ marginTop: 8 }}>Tải logo</div>
                  </div>
                </Upload>
              </Form.Item>

              <Form.Item label="Tiêu đề chính" name="title">
                <Input
                  placeholder="Nhập tiêu đề chính"
                  value={appSettings.title}
                  onChange={(e) => handleSettingChange('title', e.target.value)}
                />
              </Form.Item>

              <Form.Item label="Phụ đề" name="subtitle">
                <Input
                  placeholder="Nhập phụ đề"
                  value={appSettings.subtitle}
                  onChange={(e) => handleSettingChange('subtitle', e.target.value)}
                />
              </Form.Item>

              <Form.Item label="Màu chủ đạo" name="primaryColor">
                <Input
                  type="color"
                  value={appSettings.primaryColor}
                  onChange={(e) => handleSettingChange('primaryColor', e.target.value)}
                  style={{ width: 100, height: 40 }}
                />
              </Form.Item>

              <Form.Item label="Banner quảng cáo" name="bannerImage">
                <Upload
                  listType="picture-card"
                  maxCount={1}
                  beforeUpload={handleBannerUpload}
                  showUploadList={false}
                >
                  <div>
                    <UploadOutlined />
                    <div style={{ marginTop: 8 }}>Tải banner</div>
                  </div>
                </Upload>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  className="settings-save-btn"
                  icon={<SaveOutlined />}
                >
                  Lưu cài đặt
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const Settings = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: 'general',
      icon: <SettingOutlined />,
      label: 'Cài đặt chung',
      path: '/plugins/management/settings/general',
    },
    {
      key: 'company',
      icon: <BuildOutlined />,
      label: 'Cài đặt công ty',
      path: '/plugins/management/settings/company',
    },
    {
      key: 'interface',
      icon: <BgColorsOutlined />,
      label: 'Cài đặt giao diện',
      path: '/plugins/management/settings/interface',
    },
    {
      key: 'miniapp',
      icon: <MobileOutlined />,
      label: 'Giao diện Mini App',
      path: '/plugins/management/settings/miniapp',
    },
    {
      key: 'payment',
      icon: <CreditCardOutlined />,
      label: 'Phương thức thanh toán',
      path: '/plugins/management/settings/payment',
    },
  ];

  const getSelectedKey = () => {
    const path = location.pathname;
    if (path.includes('/settings/general')) return 'general';
    if (path.includes('/settings/company')) return 'company';
    if (path.includes('/settings/interface')) return 'interface';
    if (path.includes('/settings/miniapp')) return 'miniapp';
    if (path.includes('/settings/payment')) return 'payment';
    return 'general';
  };

  const handleMenuClick = (item: any) => {
    navigate(item.path);
  };

  return (
    <PageContainer>
      <SettingsContainer>
        <Layout className="settings-layout">
          <Sider width={280} className="settings-sider">
            <div style={{ padding: '20px 0' }}>
              <Title
                level={4}
                style={{
                  textAlign: 'center',
                  margin: '0 0 20px 0',
                  fontFamily: 'Be Vietnam Pro',
                  color: '#1a202c',
                }}
              >
                Cài đặt hệ thống
              </Title>
              <Menu
                mode="inline"
                selectedKeys={[getSelectedKey()]}
                className="settings-menu"
                items={menuItems.map((item) => ({
                  key: item.key,
                  icon: item.icon,
                  label: item.label,
                  onClick: () => handleMenuClick(item),
                }))}
              />
            </div>
          </Sider>

          <Layout>
            <Content className="settings-content">
              <Routes>
                <Route index element={<GeneralSettings />} />
                <Route path="general" element={<GeneralSettings />} />
                <Route path="company" element={<CompanySettings />} />
                <Route path="interface" element={<InterfaceSettings />} />
                <Route path="miniapp" element={<MiniAppSettings />} />
                <Route path="payment" element={<PaymentSettings />} />
              </Routes>
            </Content>
          </Layout>
        </Layout>
      </SettingsContainer>
    </PageContainer>
  );
};

export default Settings;
